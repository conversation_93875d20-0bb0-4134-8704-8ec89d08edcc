'use client'

import { useState } from 'react'
import { useAuth } from '@/components/providers/AuthProvider'
import { DatabaseComment } from '@/types'
import CommentLogin from './CommentLogin'
import CommentForm from './CommentForm'
import CommentList from './CommentList'

interface CommentSectionProps {
  postId: string
  postTitle: string
}

export default function CommentSection({ postId, postTitle }: CommentSectionProps) {
  const { user, loading } = useAuth()
  const [newComment, setNewComment] = useState<DatabaseComment | null>(null)

  const handleCommentSubmitted = (comment: DatabaseComment) => {
    setNewComment(comment)
    // Clear the new comment after a short delay to avoid duplicate displays
    setTimeout(() => setNewComment(null), 1000)
  }

  const handleLoginSuccess = () => {
    // Optionally scroll to comment form or show a success message
    console.log('Login successful, user can now comment')
  }

  if (loading) {
    return (
      <div className="mt-12 pt-8 border-t border-gray-200 dark:border-gray-700">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-6"></div>
          <div className="space-y-4">
            <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="mt-12 pt-8 border-t border-gray-200 dark:border-gray-700">
      <div className="mb-8">
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          Comments
        </h3>
        <p className="text-gray-600 dark:text-gray-400 text-sm">
          Share your thoughts about "{postTitle}"
        </p>
      </div>

      {/* Comment Form or Login Prompt */}
      <div className="mb-8">
        {user ? (
          <CommentForm 
            postId={postId} 
            onCommentSubmitted={handleCommentSubmitted}
          />
        ) : (
          <CommentLogin onLoginSuccess={handleLoginSuccess} />
        )}
      </div>

      {/* Comments List */}
      <CommentList 
        postId={postId} 
        newComment={newComment}
      />
    </div>
  )
}
