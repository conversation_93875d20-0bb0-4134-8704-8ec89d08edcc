rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Blog posts - only authenticated users can manage their own posts
    match /blog_posts/{postId} {
      allow read: if true; // Anyone can read published posts (you can add published check)
      allow write, delete: if request.auth != null && request.auth.uid == resource.data.author_id;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.author_id;
    }
    
    // Projects - only authenticated users can manage their own projects
    match /projects/{projectId} {
      allow read: if true; // Anyone can read published projects
      allow write, delete: if request.auth != null && request.auth.uid == resource.data.author_id;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.author_id;
    }
    
    // Uploaded files - only authenticated users can manage their own files
    match /uploaded_files/{fileId} {
      allow read, write, delete: if request.auth != null && request.auth.uid == resource.data.uploaded_by;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.uploaded_by;
    }
    
    // User profiles (optional - for future use)
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Comments - public can read approved, authenticated users can create, admin can moderate
    match /comments/{commentId} {
      allow read: if resource.data.status == 'approved'; // Anyone can read approved comments
      allow create: if request.auth != null && request.auth.uid == request.resource.data.user_id; // Authenticated users can comment
      allow update: if request.auth != null &&
        (request.auth.uid == resource.data.user_id || // User can edit their own comment
         isAdmin(request.auth.uid)); // Admin can moderate
      allow delete: if request.auth != null &&
        (request.auth.uid == resource.data.user_id || // User can delete their own comment
         isAdmin(request.auth.uid)); // Admin can delete
    }
  }
}

// Helper function to check if user is admin (you'll need to implement this based on your admin setup)
function isAdmin(userId) {
  // For now, we'll check if the user is the author of any blog post (simple admin check)
  // You can enhance this by creating an admin collection or checking specific user IDs
  return exists(/databases/$(database)/documents/blog_posts/$(userId));
}
