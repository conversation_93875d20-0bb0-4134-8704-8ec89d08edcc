'use client'

import { useEffect, useState } from 'react'
import { useParams } from 'next/navigation'
import { notFound } from 'next/navigation'
import Link from 'next/link'
import { getBlogPosts, getBlogPostBySlug } from '@/lib/firebase-operations'
import { processMarkdownContent } from '@/lib/markdown-client'
import { BlogPost, DatabaseBlogPost } from '@/types'
import TableOfContents from '@/components/TableOfContents'
import SocialSidebar from '@/components/SocialSidebar'
import RelatedPosts from '@/components/RelatedPosts'
import NewsletterSignup from '@/components/NewsletterSignup'
import ViewCounter from '@/components/ViewCounter'
import MobileSocialShare from '@/components/MobileSocialShare'
import OptimizedImage from '@/components/OptimizedImage'
import ReadingProgress from '@/components/ReadingProgress'
import CommentSection from '@/components/comments/CommentSection'

// Convert Firebase posts to BlogPost format
async function convertFirebasePostToBlogPost(firebasePost: DatabaseBlogPost): Promise<BlogPost> {
  // Process markdown content
  const processedContent = await processMarkdownContent(firebasePost.content)

  return {
    slug: firebasePost.slug,
    title: firebasePost.title,
    excerpt: firebasePost.excerpt,
    date: firebasePost.scheduled_for || firebasePost.created_at,
    featuredImage: firebasePost.featured_image || '/images/blog/default.png',
    content: processedContent,
    readTime: firebasePost.reading_time || 5,
    tags: firebasePost.tags || [],
    author: 'Ernst Romelo',
    categories: firebasePost.categories || [],
  }
}

export default function BlogPostPage() {
  const params = useParams()
  const slug = params.slug as string

  const [post, setPost] = useState<BlogPost | null>(null)
  const [firebasePost, setFirebasePost] = useState<DatabaseBlogPost | null>(null)
  const [allPosts, setAllPosts] = useState<BlogPost[]>([])
  const [processedContent, setProcessedContent] = useState('')
  const [loading, setLoading] = useState(true)
  const [notFound, setNotFound] = useState(false)

  useEffect(() => {
    async function loadPost() {
      try {
        const firebasePost = await getBlogPostBySlug(slug)

        if (!firebasePost) {
          setNotFound(true)
          return
        }

        setFirebasePost(firebasePost)
        const convertedPost = await convertFirebasePostToBlogPost(firebasePost)
        setPost(convertedPost)

        // Process markdown content for display
        const htmlContent = await processMarkdownContent(firebasePost.content)
        setProcessedContent(htmlContent)

        // Get all posts for related posts
        const firebasePosts = await getBlogPosts()
        const publishedPosts = firebasePosts.filter(p => p.published)
        const convertedAllPosts = await Promise.all(
          publishedPosts.map(post => convertFirebasePostToBlogPost(post))
        )
        const sortedAllPosts = convertedAllPosts.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())

        setAllPosts(sortedAllPosts)
      } catch (error) {
        console.error('Error loading post:', error)
        setNotFound(true)
      } finally {
        setLoading(false)
      }
    }

    if (slug) {
      loadPost()
    }
  }, [slug])

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (notFound || !post) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">Post Not Found</h1>
          <p className="text-gray-600 dark:text-gray-400 mb-8">The requested blog post could not be found.</p>
          <Link href="/blog" className="text-blue-600 hover:text-blue-800">
            ← Back to Blog
          </Link>
        </div>
      </div>
    )
  }

  // Add IDs to headings for table of contents (H2-H4 only, skip H1)
  const contentWithIds = processedContent.replace(
    /<h([2-4])([^>]*)>([^<]*)<\/h[2-4]>/g,
    (_, level, attrs, text) => {
      const id = text.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '')
      return `<h${level}${attrs} id="${id}">${text}</h${level}>`
    }
  )

  // Structured data for SEO
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "BlogPosting",
    "headline": post.title,
    "description": post.excerpt,
    "image": {
      "@type": "ImageObject",
      "url": post.featuredImage,
      "width": 1200,
      "height": 630
    },
    "author": {
      "@type": "Person",
      "name": post.author || "Ernst Romelo",
      "url": "https://ernestomelo.com"
    },
    "publisher": {
      "@type": "Person",
      "name": "Ernst Romelo",
      "url": "https://ernestomelo.com"
    },
    "datePublished": post.date,
    "dateModified": post.date,
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": `https://ernestomelo.com/blog/${slug}`
    },
    "url": `https://ernestomelo.com/blog/${slug}`,
    "keywords": post.tags?.join(', '),
    "articleSection": post.categories?.join(', '),
    "wordCount": processedContent.replace(/<[^>]*>/g, '').split(/\s+/).length,
    "timeRequired": `PT${post.readTime}M`
  }

  return (
    <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
      <ReadingProgress />
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Back to Blog */}
        <Link
          href="/"
          className="inline-flex items-center text-primary-600 dark:text-primary-300 hover:text-primary-400 dark:hover:text-primary-200 mb-8 transition-colors duration-300"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Back to Blog
        </Link>

        {/* Featured Image - Square crop on mobile, full size on desktop */}
        <div className="relative mb-12 -mx-4 sm:-mx-6 lg:-mx-8">
          <OptimizedImage
            src={post.featuredImage}
            alt={post.title}
            width={1200}
            height={630}
            className="w-full h-80 sm:h-96 md:h-auto object-cover object-center rounded-xl"
            priority
          />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-12 gap-12">
          {/* Social Sidebar - Hidden on mobile, shown on desktop */}
          <div className="lg:col-span-1 hidden lg:block">
            <SocialSidebar
              readTime={post.readTime}
              title={post.title}
              url={`/blog/${post.slug}`}
            />
          </div>

          {/* Main Content */}
          <div className="lg:col-span-8 col-span-1">

            {/* Enhanced Article Header */}
            <header className="mb-12">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-gray-100 mb-6 leading-tight tracking-tight">
                {post.title}
              </h1>

              <div className="flex flex-wrap items-center gap-4 text-gray-600 dark:text-gray-300 mb-8">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center mr-3">
                    <span className="text-white text-sm font-bold">ER</span>
                  </div>
                  <span className="font-medium">Ernst Romelo</span>
                </div>
                <span className="text-gray-400">•</span>
                <time dateTime={post.date} className="flex items-center">
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  {new Date(post.date).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </time>
                <span className="text-gray-400">•</span>
                <div className="flex items-center">
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>{post.readTime} min read</span>
                </div>
                <span className="text-gray-400">•</span>
                <ViewCounter slug={post.slug} />
              </div>

              {post.excerpt && (
                <div className="bg-gradient-to-r from-blue-50 to-orange-50 dark:from-blue-900/20 dark:to-orange-900/20 rounded-xl p-6 border-l-4 border-orange-500">
                  <p className="text-xl text-gray-700 dark:text-gray-200 leading-relaxed font-medium">
                    {post.excerpt}
                  </p>
                </div>
              )}
            </header>

            {/* Article Content */}
            <article
              className="blog-content prose prose-lg max-w-none"
              dangerouslySetInnerHTML={{ __html: contentWithIds }}
            />

            {/* Tags and Categories */}
            <div className="mt-12 pt-8 border-t border-gray-200 dark:border-gray-700">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Tags */}
                {post.tags && post.tags.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Tags</h3>
                    <div className="flex flex-wrap gap-2">
                      {post.tags.map((tag) => (
                        <span
                          key={tag}
                          className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors"
                        >
                          #{tag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Categories */}
                {post.categories && post.categories.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Categories</h3>
                    <div className="flex flex-wrap gap-2">
                      {post.categories.map((category) => (
                        <span
                          key={category}
                          className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 hover:bg-green-200 dark:hover:bg-green-800 transition-colors"
                        >
                          {category}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Mobile Social Sharing - Right after content */}
            <MobileSocialShare title={post.title} slug={post.slug} />

            {/* Article Navigation - Disabled for now since we're using Firebase */}

            {/* Related Posts */}
            <RelatedPosts posts={allPosts} currentPostSlug={post.slug} />

            {/* Newsletter Signup */}
            <NewsletterSignup />

            {/* Comment Section */}
            {firebasePost && (
              <CommentSection
                postId={firebasePost.id}
                postTitle={post.title}
              />
            )}
          </div>

          {/* Table of Contents Sidebar */}
          <div className="lg:col-span-3">
            <TableOfContents content={contentWithIds} />
          </div>
        </div>

        {/* Add bottom margin for footer */}
        <div className="mb-20"></div>
      </div>
    </div>
  )
}
